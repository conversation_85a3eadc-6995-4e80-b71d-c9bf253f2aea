/* Standard includes. */
#include <string.h>
#include <stdio.h>
#include <assert.h>

/* FreeRTOS includes. */
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/queue.h"
#include "freertos/event_groups.h"
#include "freertos/semphr.h"

/* ESP-IDF includes. */
#include "esp_log.h"
#include "esp_event.h"
#include "esp_heap_caps.h"
#include "sdkconfig.h"

/* coreMQTT library include. */
#include "core_mqtt.h"

/* coreMQTT-Agent include. */
#include "core_mqtt_agent.h"

/* coreMQTT-Agent network manager include. */
#include "mqtt_manager.h"
#include "mqtt_manager_events.h"

/* coreJSON include. */
#include "core_json.h"

/* Subscription manager include. */
#include "subscription_manager.h"

/* Public function declarations */
#include "reporting_task.h"

/* Demo task configurations */
#include "reporting_task_config.h"

/* Utility functions */
#include "map.h"

/* Networking components */
#include "coap_server.h"

/* Fleet provisioning demo */
#include "fleet_provisioning_with_csr.h"

/* Preprocessor definitions ***************************************************/

/* coreMQTT-Agent event group bit definitions */
#define CORE_MQTT_AGENT_CONNECTED_BIT (1 << 0)
#define CORE_MQTT_AGENT_OTA_NOT_IN_PROGRESS_BIT (1 << 1)

/* Async QoS 1 configuration */
#define MAX_CONCURRENT_QOS1_PUBLISHES 256  // 减少并发数量，避免内存压力
#define TOPIC_BUFFER_SIZE 128              // 固定topic缓冲区大小，避免动态计算
#define MAX_PUBLISH_RETRY_COUNT 3          // 最大重试次数，避免死循环
#define TOKEN_WATCHDOG_INTERVAL_MS 15000   // 令牌监控间隔：15秒（更频繁检查）
#define TOKEN_LEAK_RECOVERY_THRESHOLD 10   // 令牌泄漏恢复阈值

/**
 * @brief 安全的字符串复制函数，使用指定的内存分配器
 *
 * @param str 要复制的字符串
 * @param caps 内存分配能力标志
 * @return char* 复制的字符串指针，失败返回NULL
 */
static char* safe_strdup_caps(const char* str, uint32_t caps)
{
    if (str == NULL) return NULL;

    size_t len = strlen(str) + 1;
    char* copy = heap_caps_malloc(len, caps);
    if (copy != NULL) {
        memcpy(copy, str, len);
    }
    return copy;
}

/* Struct definitions *********************************************************/

/**
 * @brief Defines the structure to use as the command callback context in this
 * demo.
 */
struct MQTTAgentCommandContext
{
    MQTTStatus_t xReturnStatus;
    TaskHandle_t xTaskToNotify;
    uint32_t ulNotificationValue;
    void *pArgs;
};

/**
 * @brief 异步QoS 1发布的上下文结构
 */
typedef struct AsyncPublishContext
{
    MQTTPublishInfo_t xPublishInfo; // 发布信息结构，确保生命周期与上下文一致
    char *pPayload;              // 动态分配的payload，需要在回调中释放
    char *pTopicName;            // 动态分配的topic名称，需要在回调中释放
    char macAddress[MAC_ADDR_STRLEN]; // 传感器MAC地址，用于重试
    uint32_t ulPublishId;        // 发布ID，用于日志追踪
    uint32_t ulRetryCount;       // 重试计数，避免死循环
    SemaphoreHandle_t xTokenSemaphore; // 令牌信号量的引用
} AsyncPublishContext_t;

/* Global variables ***********************************************************/

/**
 * @brief Logging tag for ESP-IDF logging functions.
 */
const static char *TAG = "reporting_task";

/**
 * @brief The MQTT agent manages the MQTT contexts.  This set the handle to the
 * context used by this demo.
 */
extern MQTTAgentContext_t xGlobalMqttAgentContext;

/**
 * @brief The buffer to hold the topic filter. The topic is generated at runtime
 * by adding the task names.
 *
 * @note The topic strings must persist until unsubscribed.
 */
static char topicBuf[temppubsubandledcontrolconfigSTRING_BUFFER_LENGTH];

char *pcTopicBuffer = topicBuf;

bool hasThingName = false;

/**
 * @brief The event group used to manage coreMQTT-Agent events.
 */
static EventGroupHandle_t xNetworkEventGroup;

/**
 * @brief 令牌桶信号量，用于限制并发QoS 1发布数量
 */
static SemaphoreHandle_t xQoS1TokenSemaphore;

/**
 * @brief 令牌监控相关变量
 */
static int32_t lTokensInUse = 0;           // 当前占用的令牌数量 (0 ... MAX_CONCURRENT_QOS1_PUBLISHES)
static SemaphoreHandle_t xTokenCountMutex; // 保护令牌计数的互斥锁

/* Static function declarations ***********************************************/

/**
 * @brief ESP Event Loop library handler for coreMQTT-Agent events.
 *
 * This handles events defined in core_mqtt_agent_events.h.
 */
static void prvCoreMqttAgentEventHandler(void *pvHandlerArg,
                                         esp_event_base_t xEventBase,
                                         int32_t lEventId,
                                         void *pvEventData);

/**
 * @brief Passed into MQTTAgent_Subscribe() as the callback to execute when the
 * broker ACKs the SUBSCRIBE message.  Its implementation sends a notification
 * to the task that called MQTTAgent_Subscribe() to let the task know the
 * SUBSCRIBE operation completed.  It also sets the xReturnStatus of the
 * structure passed in as the command's context to the value of the
 * xReturnStatus parameter - which enables the task to check the status of the
 * operation.
 *
 * See https://freertos.org/mqtt/mqtt-agent-demo.html#example_mqtt_api_call
 *
 * @param[in] pxCommandContext Context of the initial command.
 * @param[in].xReturnStatus The result of the command.
 */
static void prvSubscribeCommandCallback(MQTTAgentCommandContext_t *pxCommandContext,
                                        MQTTAgentReturnInfo_t *pxReturnInfo);



/**
 * @brief 异步QoS 1发布的回调函数，用于释放资源和归还令牌
 *
 * @param[in] pxCommandContext MQTT Agent命令上下文
 * @param[in] pxReturnInfo 命令返回信息
 */
static void prvAsyncPublishCommandCallback(MQTTAgentCommandContext_t *pxCommandContext,
                                          MQTTAgentReturnInfo_t *pxReturnInfo);

/**
 * @brief 令牌监控任务，定期检查并补偿丢失的令牌
 * 使用 expectedFree = MAX - lTokensInUse 的直观算法
 *
 * @param[in] pvParameters 任务参数（未使用）
 */
static void prvTokenWatchdogTask(void *pvParameters);

/**
 * @brief 安全地获取令牌并递增占用计数
 *
 * @param[in] xBlockTime 阻塞时间
 * @return BaseType_t pdTRUE表示成功获取令牌
 */
static BaseType_t prvTakeTokenSafely(TickType_t xBlockTime);

/**
 * @brief 安全地归还令牌并递减占用计数
 */
static void prvGiveTokenSafely(void);

/**
 * @brief Called by the task to wait for a notification from a callback function
 * after the task first executes either MQTTAgent_Publish()* or
 * MQTTAgent_Subscribe().
 *
 * See https://freertos.org/mqtt/mqtt-agent-demo.html#example_mqtt_api_call
 *
 * @param[in] pxCommandContext Context of the initial command.
 * @param[out] pulNotifiedValue The task's notification value after it receives
 * a notification from the callback.
 *
 * @return pdTRUE if the task received a notification, otherwise pdFALSE.
 */
static BaseType_t prvWaitForCommandAcknowledgment(uint32_t *pulNotifiedValue);

/**
 * @brief Passed into MQTTAgent_Subscribe() as the callback to execute when
 * there is an incoming publish on the topic being subscribed to.  Its
 * implementation just logs information about the incoming publish including
 * the publish messages source topic and payload.
 *
 * See https://freertos.org/mqtt/mqtt-agent-demo.html#example_mqtt_api_call
 *
 * @param[in] pvIncomingPublishCallbackContext Context of the initial command.
 * @param[in] pxPublishInfo Deserialized publish.
 */
static void prvIncomingPublishCallback(void *pvIncomingPublishCallbackContext,
                                       MQTTPublishInfo_t *pxPublishInfo);

/**
 * @brief Subscribe to the topic the demo task will also publish to - that
 * results in all outgoing publishes being published back to the task
 * (effectively echoed back).
 *
 * @param[in] xQoS The quality of service (QoS) to use.  Can be zero or one
 * for all MQTT brokers.  Can also be QoS2 if supported by the broker.  AWS IoT
 * does not support QoS2.
 */
static bool prvSubscribeToTopic(MQTTQoS_t xQoS,
                                char *pcTopicFilter);

/**
 * @brief The function that implements the task demonstrated by this file.
 */
static void prvTempSubPubAndLEDControlTask(void *pvParameters);

/* Static function definitions ************************************************/



static BaseType_t prvTakeTokenSafely(TickType_t xBlockTime)
{
    BaseType_t xResult = xSemaphoreTake(xQoS1TokenSemaphore, xBlockTime);
    if (xResult == pdTRUE)
    {
        /* 安全地递增当前占用计数 */
        if (xSemaphoreTake(xTokenCountMutex, portMAX_DELAY) == pdTRUE)
        {
            lTokensInUse++;
            xSemaphoreGive(xTokenCountMutex);
        }
    }
    return xResult;
}

static void prvGiveTokenSafely(void)
{
    /* 安全地递减当前占用计数 */
    if (xSemaphoreTake(xTokenCountMutex, portMAX_DELAY) == pdTRUE)
    {
        lTokensInUse--;
        /* 确保计数不会变为负数 */
        if (lTokensInUse < 0)
        {
            ESP_LOGE(TAG, "Token count went negative! Resetting to 0");
            lTokensInUse = 0;
        }
        xSemaphoreGive(xTokenCountMutex);
    }

    /* 归还令牌到信号量 */
    xSemaphoreGive(xQoS1TokenSemaphore);
}

static void prvTokenWatchdogTask(void *pvParameters)
{
    (void)pvParameters;

    ESP_LOGI(TAG, "Token watchdog task started");

    while (1)
    {
        vTaskDelay(pdMS_TO_TICKS(TOKEN_WATCHDOG_INTERVAL_MS));

        /* 获取当前占用令牌数快照 */
        int32_t tokensInUse;
        if (xSemaphoreTake(xTokenCountMutex, portMAX_DELAY) == pdTRUE)
        {
            tokensInUse = lTokensInUse;
            xSemaphoreGive(xTokenCountMutex);
        }
        else
        {
            ESP_LOGE(TAG, "Failed to acquire token count mutex in watchdog");
            continue;
        }

        /* 获取信号量当前可用令牌数 */
        UBaseType_t availableTokens = uxSemaphoreGetCount(xQoS1TokenSemaphore);

        /* 计算期望的可用令牌数 */
        int32_t expectedFree = MAX_CONCURRENT_QOS1_PUBLISHES - tokensInUse;

        /* 检查是否有令牌泄漏 */
        if ((int32_t)availableTokens < expectedFree)
        {
            int32_t lostTokens = expectedFree - (int32_t)availableTokens;
            ESP_LOGW(TAG, "Token leak detected! Lost: %ld, InUse: %ld, Available: %u, Expected: %ld",
                     lostTokens, tokensInUse, (unsigned int)availableTokens, expectedFree);

            /* 限制单次恢复的令牌数量，避免过度补偿 */
            int32_t tokensToRecover = (lostTokens > TOKEN_LEAK_RECOVERY_THRESHOLD) ? TOKEN_LEAK_RECOVERY_THRESHOLD : lostTokens;

            /* 补偿丢失的令牌 */
            for (int32_t i = 0; i < tokensToRecover; i++)
            {
                xSemaphoreGive(xQoS1TokenSemaphore);
            }

            ESP_LOGI(TAG, "Compensated %ld lost tokens (requested: %ld)", tokensToRecover, lostTokens);

            /* 如果泄漏严重，重置计数器 */
            if (lostTokens > TOKEN_LEAK_RECOVERY_THRESHOLD * 2)
            {
                ESP_LOGE(TAG, "Severe token leak detected, resetting token counter");
                lTokensInUse = MAX_CONCURRENT_QOS1_PUBLISHES - uxSemaphoreGetCount(xQoS1TokenSemaphore);
            }
        }
        else if ((int32_t)availableTokens > expectedFree)
        {
            /* 这种情况可能发生在计数不一致时 */
            int32_t excessTokens = (int32_t)availableTokens - expectedFree;
            ESP_LOGW(TAG, "Excess tokens detected! Excess: %ld, InUse: %ld, Available: %u, Expected: %ld",
                     excessTokens, tokensInUse, (unsigned int)availableTokens, expectedFree);

            /* 修正计数器 */
            if (excessTokens > TOKEN_LEAK_RECOVERY_THRESHOLD)
            {
                ESP_LOGW(TAG, "Correcting token counter due to excess tokens");
                lTokensInUse = MAX_CONCURRENT_QOS1_PUBLISHES - (int32_t)availableTokens;
            }

            /* 可选择性地调整占用计数或记录异常 */
            if (excessTokens <= MAX_CONCURRENT_QOS1_PUBLISHES / 4) // 如果差异不大，调整计数
            {
                if (xSemaphoreTake(xTokenCountMutex, portMAX_DELAY) == pdTRUE)
                {
                    lTokensInUse = MAX_CONCURRENT_QOS1_PUBLISHES - (int32_t)availableTokens;
                    if (lTokensInUse < 0) lTokensInUse = 0;
                    ESP_LOGI(TAG, "Adjusted tokens in use count to %ld", lTokensInUse);
                    xSemaphoreGive(xTokenCountMutex);
                }
            }
        }
        else
        {
            /* 正常情况，可选择性记录调试信息 */
            ESP_LOGD(TAG, "Token status OK - InUse: %ld, Available: %u, Expected: %ld",
                     tokensInUse, (unsigned int)availableTokens, expectedFree);
        }
    }
}

static void prvAsyncPublishCommandCallback(MQTTAgentCommandContext_t *pxCommandContext,
                                          MQTTAgentReturnInfo_t *pxReturnInfo)
{
    if (pxCommandContext == NULL || pxCommandContext->pArgs == NULL)
    {
        ESP_LOGE(TAG, "Async publish callback received NULL context or args");
        return;
    }

    /* 从命令上下文中获取异步发布上下文 */
    AsyncPublishContext_t *pxAsyncContext = (AsyncPublishContext_t *)pxCommandContext->pArgs;

    /* 记录发布结果 */
    if (pxReturnInfo->returnCode == MQTTSuccess)
    {
        //ESP_LOGI(TAG, "Async publish %" PRIu32 " completed successfully", pxAsyncContext->ulPublishId);

        /* 成功发布，重置重试计数 */
        resetRetryCountByMac(pxAsyncContext->macAddress);
    }
    else
    {
        ESP_LOGE(TAG, "Async publish %" PRIu32 " failed with code %d, retry count: %" PRIu32,
                 pxAsyncContext->ulPublishId, pxReturnInfo->returnCode, pxAsyncContext->ulRetryCount);

        /* 实现"至少一次"可靠性重试逻辑 */
        if (pxAsyncContext->ulRetryCount < MAX_PUBLISH_RETRY_COUNT)
        {
            /* 递增重试计数 */
            incrementRetryCountByMac(pxAsyncContext->macAddress);

            /* 重试：将MAC地址重新放回队列 */
            BaseType_t xQueueResult = xQueueSend(sensor_update_queue, pxAsyncContext->macAddress, 0);
            if (xQueueResult == pdTRUE)
            {
                ESP_LOGW(TAG, "Retrying publish for sensor %s (attempt %" PRIu32 "/%d)",
                         pxAsyncContext->macAddress, pxAsyncContext->ulRetryCount + 1, MAX_PUBLISH_RETRY_COUNT);

                /* 重试成功，不释放令牌，让重试使用同一个令牌 */
                /* 释放当前上下文的内存，但保留令牌给重试使用 */
                if (pxAsyncContext->pPayload != NULL)
                {
                    heap_caps_free(pxAsyncContext->pPayload);
                }
                if (pxAsyncContext->pTopicName != NULL)
                {
                    heap_caps_free(pxAsyncContext->pTopicName);
                }
                heap_caps_free(pxAsyncContext);
                heap_caps_free(pxCommandContext);

                /* 注意：不归还令牌，让重试使用 */
                return;
            }
            else
            {
                ESP_LOGE(TAG, "Failed to queue retry for sensor %s, queue full - releasing token", pxAsyncContext->macAddress);
                /* 队列满时必须归还令牌，避免令牌泄漏 */
                prvGiveTokenSafely();
            }
        }
        else
        {
            ESP_LOGE(TAG, "Max retry count reached for sensor %s, giving up", pxAsyncContext->macAddress);
            /* 达到最大重试次数，重置计数器 */
            resetRetryCountByMac(pxAsyncContext->macAddress);
        }
    }

    /* 释放动态分配的payload和topic内存 */
    if (pxAsyncContext->pPayload != NULL)
    {
        heap_caps_free(pxAsyncContext->pPayload);
    }
    if (pxAsyncContext->pTopicName != NULL)
    {
        heap_caps_free(pxAsyncContext->pTopicName);
    }

    /* 安全地归还令牌 */
    if (pxAsyncContext->xTokenSemaphore != NULL)
    {
        prvGiveTokenSafely();
    }

    /* 释放异步上下文结构本身 */
    heap_caps_free(pxAsyncContext);

    /* 释放命令上下文结构 */
    heap_caps_free(pxCommandContext);
}

static void prvSubscribeCommandCallback(MQTTAgentCommandContext_t *pxCommandContext,
                                        MQTTAgentReturnInfo_t *pxReturnInfo)
{
    bool xSubscriptionAdded = false;
    MQTTAgentSubscribeArgs_t *pxSubscribeArgs = (MQTTAgentSubscribeArgs_t *)pxCommandContext->pArgs;

    /* Store the result in the application defined context so the task that
     * initiated the subscribe can check the operation's status.  Also send the
     * status as the notification value.  These things are just done for
     * demonstration purposes. */
    pxCommandContext->xReturnStatus = pxReturnInfo->returnCode;

    /* Check if the subscribe operation is a success. Only one topic is
     * subscribed by this demo. */
    if (pxReturnInfo->returnCode == MQTTSuccess)
    {
        /* Add subscription so that incoming publishes are routed to the application
         * callback. */
        xSubscriptionAdded = addSubscription((SubscriptionElement_t *)xGlobalMqttAgentContext.pIncomingCallbackContext,
                                             pxSubscribeArgs->pSubscribeInfo->pTopicFilter,
                                             pxSubscribeArgs->pSubscribeInfo->topicFilterLength,
                                             prvIncomingPublishCallback,
                                             NULL);

        if (xSubscriptionAdded == false)
        {
            ESP_LOGE(TAG,
                     "Failed to register an incoming publish callback for topic %.*s.",
                     pxSubscribeArgs->pSubscribeInfo->topicFilterLength,
                     pxSubscribeArgs->pSubscribeInfo->pTopicFilter);
        }
    }

    xTaskNotify(pxCommandContext->xTaskToNotify,
                (uint32_t)(pxReturnInfo->returnCode),
                eSetValueWithOverwrite);
}

static BaseType_t prvWaitForCommandAcknowledgment(uint32_t *pulNotifiedValue)
{
    BaseType_t xReturn;

    /* Wait for this task to get notified, passing out the value it gets
     * notified with. */
    xReturn = xTaskNotifyWait(0,
                              0,
                              pulNotifiedValue,
                              portMAX_DELAY);
    return xReturn;
}

static void prvParseIncomingPublish(char *publishPayload, size_t publishPayloadLength)
{
    char *outValue = NULL;
    uint32_t outValueLength = 0U;
    JSONStatus_t result = JSONSuccess;
    char deviceId[18];            // The length of the MAC address is 17 + 1 (for the terminator)
    uint32_t intervalSeconds = 0; // Used to store sleep time

    result = JSON_Validate((const char *)publishPayload, publishPayloadLength);

    if (result == JSONSuccess)
    {
        // extract deviceId
        result = JSON_Search((char *)publishPayload,
                             publishPayloadLength,
                             "sen",
                             sizeof("sen") - 1,
                             &outValue,
                             (size_t *)&outValueLength);

        if (result == JSONSuccess)
        {
            // 将字符串终止符添加到 outValue 中
            char valueBuffer[outValueLength + 1];
            strncpy(valueBuffer, outValue, outValueLength);
            valueBuffer[outValueLength] = '\0';

            // 转换字符串为浮点数
            sen = strtof(valueBuffer, NULL);
            return;
        }

        // extract deviceId
        result = JSON_Search((char *)publishPayload,
                             publishPayloadLength,
                             "deviceId",
                             sizeof("deviceId") - 1,
                             &outValue,
                             (size_t *)&outValueLength);

        if (result == JSONSuccess)
        {
            // Copy the deviceId value
            strncpy(deviceId, outValue, sizeof(deviceId) - 1);
            deviceId[sizeof(deviceId) - 2] = '\0'; // Ensures null termination
        }
        else
        {
            return;
        }

        // extract intervalSeconds
        result = JSON_Search((char *)publishPayload,
                             publishPayloadLength,
                             "updateIntervalSeconds",
                             sizeof("updateIntervalSeconds") - 1,
                             &outValue,
                             (size_t *)&outValueLength);

        if (result == JSONSuccess)
        {
            // Convert intervalSeconds to an integer
            intervalSeconds = (uint32_t)strtoul(outValue, NULL, 10);
        }
        else
        {
            return;
        }

        // Call addOrUpdateSleepTime to update the sleep time
        addOrUpdateSleepTime(deviceId, intervalSeconds);
    }
    else
    {
        ESP_LOGE(TAG, "The JSON document is invalid!");
        return;
    }
}

static void prvIncomingPublishCallback(void *pvIncomingPublishCallbackContext,
                                       MQTTPublishInfo_t *pxPublishInfo)
{
    static char cTerminatedString[temppubsubandledcontrolconfigSTRING_BUFFER_LENGTH];

    (void)pvIncomingPublishCallbackContext;

    /* Create a message that contains the incoming MQTT payload to the logger,
     * terminating the string first. */
    if (pxPublishInfo->payloadLength < temppubsubandledcontrolconfigSTRING_BUFFER_LENGTH)
    {
        memcpy((void *)cTerminatedString, pxPublishInfo->pPayload, pxPublishInfo->payloadLength);
        cTerminatedString[pxPublishInfo->payloadLength] = 0x00;
    }
    else
    {
        memcpy((void *)cTerminatedString, pxPublishInfo->pPayload, temppubsubandledcontrolconfigSTRING_BUFFER_LENGTH);
        cTerminatedString[temppubsubandledcontrolconfigSTRING_BUFFER_LENGTH - 1] = 0x00;
    }

    // ESP_LOGI( TAG,
    //           "Received incoming publish message %s",
    //           cTerminatedString );

    ESP_LOGI(TAG, "Received incoming publish message");

    prvParseIncomingPublish((char *)pxPublishInfo->pPayload, pxPublishInfo->payloadLength);
}

static bool prvSubscribeToTopic(MQTTQoS_t xQoS,
                                char *pcTopicFilter)
{
    MQTTStatus_t xCommandAdded;
    BaseType_t xCommandAcknowledged = pdFALSE;
    MQTTAgentSubscribeArgs_t xSubscribeArgs;
    MQTTSubscribeInfo_t xSubscribeInfo;
    static int32_t ulNextSubscribeMessageID = 0;
    MQTTAgentCommandContext_t xApplicationDefinedContext = {0UL};
    MQTTAgentCommandInfo_t xCommandParams = {0UL};

    /* Create a unique number of the subscribe that is about to be sent.  The number
     * is used as the command context and is sent back to this task as a notification
     * in the callback that executed upon receipt of the subscription acknowledgment.
     * That way this task can match an acknowledgment to a subscription. */
    xTaskNotifyStateClear(NULL);

    ulNextSubscribeMessageID++;

    /* Complete the subscribe information.  The topic string must persist for
     * duration of subscription! */
    xSubscribeInfo.pTopicFilter = pcTopicFilter;
    xSubscribeInfo.topicFilterLength = (uint16_t)strlen(pcTopicFilter);
    xSubscribeInfo.qos = xQoS;
    xSubscribeArgs.pSubscribeInfo = &xSubscribeInfo;
    xSubscribeArgs.numSubscriptions = 1;

    /* Complete an application defined context associated with this subscribe message.
     * This gets updated in the callback function so the variable must persist until
     * the callback executes. */
    xApplicationDefinedContext.ulNotificationValue = ulNextSubscribeMessageID;
    xApplicationDefinedContext.xTaskToNotify = xTaskGetCurrentTaskHandle();
    xApplicationDefinedContext.pArgs = (void *)&xSubscribeArgs;

    xCommandParams.blockTimeMs = temppubsubandledcontrolconfigMAX_COMMAND_SEND_BLOCK_TIME_MS;
    xCommandParams.cmdCompleteCallback = prvSubscribeCommandCallback;
    xCommandParams.pCmdCompleteCallbackContext = (void *)&xApplicationDefinedContext;

    /* Loop in case the queue used to communicate with the MQTT agent is full and
     * attempts to post to it time out.  The queue will not become full if the
     * priority of the MQTT agent task is higher than the priority of the task
     * calling this function. */
    ESP_LOGI(TAG,
             "Sending subscribe request to agent for topic filter: %s with id %d",
             pcTopicFilter,
             (int)ulNextSubscribeMessageID);

    do
    {
        xCommandAdded = MQTTAgent_Subscribe(&xGlobalMqttAgentContext,
                                            &xSubscribeArgs,
                                            &xCommandParams);
    } while (xCommandAdded != MQTTSuccess);

    /* Wait for acks to the subscribe message - this is optional but done here
     * so the code below can check the notification sent by the callback matches
     * the ulNextSubscribeMessageID value set in the context above. */
    xCommandAcknowledged = prvWaitForCommandAcknowledgment(NULL);

    /* Check both ways the status was passed back just for demonstration
     * purposes. */
    if ((xCommandAcknowledged != pdTRUE) ||
        (xApplicationDefinedContext.xReturnStatus != MQTTSuccess))
    {
        ESP_LOGE(TAG,
                 "Error or timed out waiting for ack to subscribe message topic %s",
                 pcTopicFilter);
    }
    else
    {
        ESP_LOGI(TAG,
                 "Received subscribe ack for topic %s containing ID %d",
                 pcTopicFilter,
                 (int)xApplicationDefinedContext.ulNotificationValue);
    }

    return xCommandAcknowledged;
}

void writeThingName(void)
{
    /* Create a topic name for this task to publish to. */
    snprintf(pcTopicBuffer,
             temppubsubandledcontrolconfigSTRING_BUFFER_LENGTH,
             "gateway/%s",
             thingName);
    hasThingName = true;
}

static void prvTempSubPubAndLEDControlTask(void *pvParameters)
{
    uint32_t ulValueToNotify = 0UL;
    MQTTStatus_t xCommandAdded;
    MQTTQoS_t xQoS;



    /* Initialize the coreMQTT-Agent event group. */
    xNetworkEventGroup = xEventGroupCreate();
    xEventGroupSetBits(xNetworkEventGroup,
                       CORE_MQTT_AGENT_OTA_NOT_IN_PROGRESS_BIT);

    /* 初始化QoS 1令牌信号量 */
    xQoS1TokenSemaphore = xSemaphoreCreateCounting(MAX_CONCURRENT_QOS1_PUBLISHES,
                                                   MAX_CONCURRENT_QOS1_PUBLISHES);
    if (xQoS1TokenSemaphore == NULL)
    {
        ESP_LOGE(TAG, "Failed to create QoS 1 token semaphore");
        vTaskDelete(NULL);
        return;
    }
    ESP_LOGI(TAG, "QoS 1 token semaphore created with %d tokens", MAX_CONCURRENT_QOS1_PUBLISHES);

    /* 初始化令牌计数互斥锁 */
    xTokenCountMutex = xSemaphoreCreateMutex();
    if (xTokenCountMutex == NULL)
    {
        ESP_LOGE(TAG, "Failed to create token count mutex");
        vTaskDelete(NULL);
        return;
    }

    /* 启动令牌监控任务 */
    BaseType_t xWatchdogResult = xTaskCreateWithCaps(
        prvTokenWatchdogTask,
        "TokenWatchdog",
        4096,  // 栈大小
        NULL,
        tskIDLE_PRIORITY + 1,  // 低优先级
        NULL,
        MALLOC_CAP_SPIRAM
    );

    if (xWatchdogResult != pdPASS) {
        ESP_LOGW(TAG, "Failed to create token watchdog task with PSRAM, trying internal RAM");
        xTaskCreate(prvTokenWatchdogTask,
                    "TokenWatchdog",
                    4096,
                    NULL,
                    tskIDLE_PRIORITY + 1,
                    NULL);
    } else {
        ESP_LOGI(TAG, "Token watchdog task created with PSRAM stack");
    }

    /* Register coreMQTT-Agent event handler. */
    xCoreMqttAgentManagerRegisterHandler(prvCoreMqttAgentEventHandler);

    xQoS = (MQTTQoS_t)temppubsubandledcontrolconfigQOS_LEVEL;

    while (!hasThingName)
    {
        vTaskDelay(pdMS_TO_TICKS(1000));
    }

    /* Subscribe to the same topic to which this task will publish.  That will
     * result in each published message being published from the server back to
     * the target. */
    prvSubscribeToTopic(xQoS, pcTopicBuffer);

    ulValueToNotify = 0UL;

    /* For an infinite number of publishes */
    while (1)
    {
        char mac_address[MAC_ADDR_STRLEN];

        // Wait for a sensor update MAC address from the queue
        if (xQueueReceive(sensor_update_queue, mac_address, portMAX_DELAY) == pdTRUE)
        {
            /* 安全地获取令牌，如果没有可用令牌则阻塞 */
            if (prvTakeTokenSafely(portMAX_DELAY) != pdTRUE)
            {
                ESP_LOGE(TAG, "Failed to acquire QoS 1 token for sensor %s", mac_address);
                continue;
            }

            /* 为异步发布创建上下文，使用PSRAM */
            AsyncPublishContext_t *pAsyncContext = heap_caps_malloc(sizeof(AsyncPublishContext_t),
                                                                   MALLOC_CAP_SPIRAM);
            if (pAsyncContext == NULL)
            {
                ESP_LOGE(TAG, "Failed to allocate async context for sensor %s", mac_address);
                prvGiveTokenSafely(); // 安全地归还令牌
                continue;
            }

            /* 初始化异步上下文 */
            memset(pAsyncContext, 0, sizeof(AsyncPublishContext_t));
            strncpy(pAsyncContext->macAddress, mac_address, MAC_ADDR_STRLEN - 1);
            pAsyncContext->macAddress[MAC_ADDR_STRLEN - 1] = '\0';

            /* 检查是否为重试（通过检查传感器数据中的重试计数） */
            uint32_t currentRetryCount = getRetryCountByMac(mac_address);
            pAsyncContext->ulRetryCount = currentRetryCount;

            /* 获取JSON数据 */
            char *jsonData = arrayToJson(mac_address);
            if (jsonData == NULL)
            {
                ESP_LOGE(TAG, "Failed to convert vector to JSON for sensorMAC: %s", mac_address);
                heap_caps_free(pAsyncContext);
                prvGiveTokenSafely(); // 安全地归还令牌
                continue;
            }

            /* 使用安全字符串复制函数，确保独立副本 */
            pAsyncContext->pPayload = safe_strdup_caps(jsonData, MALLOC_CAP_SPIRAM);
            if (pAsyncContext->pPayload == NULL)
            {
                ESP_LOGE(TAG, "Failed to duplicate payload memory for sensor %s", mac_address);
                heap_caps_free(jsonData);
                heap_caps_free(pAsyncContext);
                prvGiveTokenSafely(); // 安全地归还令牌
                continue;
            }
            heap_caps_free(jsonData); // 释放临时JSON数据

            /* 固定长度分配topic内存，避免动态计算 */
            pAsyncContext->pTopicName = heap_caps_malloc(TOPIC_BUFFER_SIZE, MALLOC_CAP_SPIRAM);
            if (pAsyncContext->pTopicName == NULL)
            {
                ESP_LOGE(TAG, "Failed to allocate topic memory for sensor %s", mac_address);
                heap_caps_free(pAsyncContext->pPayload);
                heap_caps_free(pAsyncContext);
                prvGiveTokenSafely(); // 安全地归还令牌
                continue;
            }

            /* 构造topic字符串，确保不会溢出 */
            int topicLen = snprintf(pAsyncContext->pTopicName, TOPIC_BUFFER_SIZE,
                                   "gateway/%s/sensor/%s/measurement/current", thingName, mac_address);
            if (topicLen >= TOPIC_BUFFER_SIZE)
            {
                ESP_LOGE(TAG, "Topic string too long for sensor %s, truncated", mac_address);
                // 继续执行，因为字符串已被截断但仍然有效
            }

            /* 设置异步上下文 */
            pAsyncContext->ulPublishId = ulValueToNotify++;  // 立即递增ID避免重复
            pAsyncContext->xTokenSemaphore = xQoS1TokenSemaphore;

            /* 创建命令上下文，使用PSRAM */
            MQTTAgentCommandContext_t *pxCmdContext = heap_caps_malloc(sizeof(MQTTAgentCommandContext_t),
                                                                      MALLOC_CAP_SPIRAM);
            if (pxCmdContext == NULL)
            {
                ESP_LOGE(TAG, "Failed to allocate command context for sensor %s", mac_address);
                heap_caps_free(pAsyncContext->pPayload);
                heap_caps_free(pAsyncContext->pTopicName);
                heap_caps_free(pAsyncContext);
                prvGiveTokenSafely(); // 安全地归还令牌
                continue;
            }
            memset(pxCmdContext, 0, sizeof(MQTTAgentCommandContext_t));
            pxCmdContext->pArgs = pAsyncContext;

            /* 设置异步上下文中的发布信息结构，确保长生命周期 */
            memset(&pAsyncContext->xPublishInfo, 0, sizeof(MQTTPublishInfo_t));
            pAsyncContext->xPublishInfo.qos = xQoS;
            pAsyncContext->xPublishInfo.retain = false;  // 明确设置retain字段
            pAsyncContext->xPublishInfo.dup = false;     // 明确设置dup字段
            pAsyncContext->xPublishInfo.payloadLength = (uint16_t)strlen(pAsyncContext->pPayload);
            pAsyncContext->xPublishInfo.pTopicName = pAsyncContext->pTopicName;
            pAsyncContext->xPublishInfo.topicNameLength = (uint16_t)strlen(pAsyncContext->pTopicName);
            pAsyncContext->xPublishInfo.pPayload = pAsyncContext->pPayload;

            /* 设置异步命令参数 */
            MQTTAgentCommandInfo_t xAsyncCommandParams = {0UL};
            xAsyncCommandParams.blockTimeMs = temppubsubandledcontrolconfigMAX_COMMAND_SEND_BLOCK_TIME_MS;
            xAsyncCommandParams.cmdCompleteCallback = prvAsyncPublishCommandCallback;
            xAsyncCommandParams.pCmdCompleteCallbackContext = pxCmdContext;

            /* Wait for coreMQTT-Agent task to have working network connection and
             * not be performing an OTA update. */
            xEventGroupWaitBits(xNetworkEventGroup,
                                CORE_MQTT_AGENT_CONNECTED_BIT | CORE_MQTT_AGENT_OTA_NOT_IN_PROGRESS_BIT,
                                pdFALSE,
                                pdTRUE,
                                portMAX_DELAY);

            /* 异步发布，直接使用已准备好的发布信息结构 */
            xCommandAdded = MQTTAgent_Publish(&xGlobalMqttAgentContext,
                                              &pAsyncContext->xPublishInfo,
                                              &xAsyncCommandParams);

            if (xCommandAdded == MQTTSuccess)
            {
                //ESP_LOGI(TAG, "Async publish %" PRIu32 " queued for sensor %s", pAsyncContext->ulPublishId, mac_address);
            }
            else
            {
                ESP_LOGE(TAG, "Failed to queue async publish for sensor %s, error: %d",
                         mac_address, xCommandAdded);

                /* 根据错误类型决定是否重试 */
                if (xCommandAdded == MQTTNoMemory && currentRetryCount < MAX_PUBLISH_RETRY_COUNT)
                {
                    /* 内存不足时，延迟重试 */
                    ESP_LOGW(TAG, "MQTT queue full, delaying retry for sensor %s", mac_address);
                    vTaskDelay(pdMS_TO_TICKS(100)); // 100ms延迟

                    /* 重新放回队列进行重试 */
                    if (xQueueSend(sensor_update_queue, mac_address, 0) == pdTRUE)
                    {
                        incrementRetryCountByMac(mac_address);
                        /* 清理当前上下文，但保留令牌给重试使用 */
                        heap_caps_free(pAsyncContext->pPayload);
                        heap_caps_free(pAsyncContext->pTopicName);
                        heap_caps_free(pAsyncContext);
                        heap_caps_free(pxCmdContext);
                        /* 不归还令牌，让重试使用 */
                        continue;
                    }
                }

                /* 发布失败，清理资源 */
                heap_caps_free(pAsyncContext->pPayload);
                heap_caps_free(pAsyncContext->pTopicName);
                heap_caps_free(pAsyncContext);
                heap_caps_free(pxCmdContext);
                prvGiveTokenSafely(); // 安全地归还令牌
            }


        }

        /* 日志上传已移至独立的日志上传任务，此处不再处理日志 */
    }

    vTaskDelete(NULL);
}

static void prvCoreMqttAgentEventHandler(void *pvHandlerArg,
                                         esp_event_base_t xEventBase,
                                         int32_t lEventId,
                                         void *pvEventData)
{
    (void)pvHandlerArg;
    (void)xEventBase;
    (void)pvEventData;

    switch (lEventId)
    {
    case CORE_MQTT_AGENT_CONNECTED_EVENT:
        ESP_LOGI(TAG,
                 "coreMQTT-Agent connected.");
        xEventGroupSetBits(xNetworkEventGroup,
                           CORE_MQTT_AGENT_CONNECTED_BIT);
        break;

    case CORE_MQTT_AGENT_DISCONNECTED_EVENT:
        ESP_LOGI(TAG,
                 "coreMQTT-Agent disconnected. Preventing coreMQTT-Agent "
                 "commands from being enqueued.");
        xEventGroupClearBits(xNetworkEventGroup,
                             CORE_MQTT_AGENT_CONNECTED_BIT);
        break;

    case CORE_MQTT_AGENT_OTA_STARTED_EVENT:
        ESP_LOGI(TAG,
                 "OTA started. Preventing coreMQTT-Agent commands from "
                 "being enqueued.");
        xEventGroupClearBits(xNetworkEventGroup,
                             CORE_MQTT_AGENT_OTA_NOT_IN_PROGRESS_BIT);
        break;

    case CORE_MQTT_AGENT_OTA_STOPPED_EVENT:
        ESP_LOGI(TAG,
                 "OTA stopped. No longer preventing coreMQTT-Agent "
                 "commands from being enqueued.");
        xEventGroupSetBits(xNetworkEventGroup,
                           CORE_MQTT_AGENT_OTA_NOT_IN_PROGRESS_BIT);
        break;

    default:
        ESP_LOGE(TAG,
                 "coreMQTT-Agent event handler received unexpected event: %" PRIu32 "",
                 lEventId);
        break;
    }
}

/* Public function definitions ************************************************/

void vStartReporting(void)
{
    // Try to create task with PSRAM stack first
    BaseType_t result = xTaskCreateWithCaps(
        prvTempSubPubAndLEDControlTask,
        "currentSensor",
        temppubsubandledcontrolconfigTASK_STACK_SIZE,
        NULL,
        temppubsubandledcontrolconfigTASK_PRIORITY,
        NULL,
        MALLOC_CAP_SPIRAM
    );

    // Fallback to internal RAM if PSRAM stack creation fails
    if (result != pdPASS) {
        ESP_LOGW("REPORTING", "Failed to create reporting task with PSRAM stack, trying internal RAM");
        xTaskCreate(prvTempSubPubAndLEDControlTask,
                    "currentSensor",
                    temppubsubandledcontrolconfigTASK_STACK_SIZE,
                    NULL,
                    temppubsubandledcontrolconfigTASK_PRIORITY,
                    NULL);
    } else {
        ESP_LOGI("REPORTING", "Reporting task created with PSRAM stack");
    }
}
